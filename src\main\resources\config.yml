# FixBlockBreak Plugin Configuration
# Cấu hình plugin để fix vấn đề kẹt block khi đào ore

# Cài đặt chung
general:
  # Bật/tắt plugin
  enabled: true
  
  # Hiển thị debug messages
  debug: false
  
  # Bật/tắt thông báo cho player khi block bị kẹt
  notify-player: true

# Cài đặt block breaking - Tối ưu cho hiệu suất cao
block-breaking:
  # KHÔNG sử dụng cooldown để tăng hiệu suất đào
  break-cooldown: 0

  # Cho phép đào liên tục không giới hạn
  max-consecutive-breaks: 999999

  # Không delay khi đào liên tục
  spam-delay: 0

  # B<PERSON> kính coi là "cùng khu vực" (blocks)
  same-area-radius: 10.0

  # Thời gian chờ trước khi kiểm tra block kẹt (ticks) - giảm để phản ứng nhanh hơn
  stuck-check-delay: 1

# Danh sách các loại ore được xử lý
ore-types:
  - COAL_ORE
  - IRON_ORE
  - GOLD_ORE
  - DIAMOND_ORE
  - EMERALD_ORE
  - LAPIS_ORE
  - REDSTONE_ORE
  - COPPER_ORE
  - DEEPSLATE_COAL_ORE
  - DEEPSLATE_IRON_ORE
  - DEEPSLATE_GOLD_ORE
  - DEEPSLATE_DIAMOND_ORE
  - DEEPSLATE_EMERALD_ORE
  - DEEPSLATE_LAPIS_ORE
  - DEEPSLATE_REDSTONE_ORE
  - DEEPSLATE_COPPER_ORE
  - NETHER_GOLD_ORE
  - NETHER_QUARTZ_ORE
  - ANCIENT_DEBRIS

# Tối ưu performance cho hiệu suất đào cao
performance:
  # Thời gian cleanup data cũ (seconds) - giảm để tiết kiệm memory
  cleanup-interval: 60

  # Threshold để cleanup data cũ (seconds) - giảm để responsive hơn
  cleanup-threshold: 60

  # Bật async processing để không lag server
  async-processing: true

  # Batch size cho xử lý nhiều blocks cùng lúc
  batch-size: 50

  # Thời gian check stuck mining animation (seconds)
  stuck-mining-check: 3

# Thông báo
messages:
  block-stuck: "&cPhát hiện block kẹt! Đang tự động sửa..."
  too-fast: "&eĐào quá nhanh! Vui lòng chậm lại một chút."
  plugin-reloaded: "&aPlugin đã được reload thành công!"
