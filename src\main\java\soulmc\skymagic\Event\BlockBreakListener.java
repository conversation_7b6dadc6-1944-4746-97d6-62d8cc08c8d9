package soulmc.skymagic.Event;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.block.Action;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import soulmc.skymagic.utils.PerformanceMonitor;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.EnumSet;

public class BlockBreakListener implements Listener {
    
    private final Plugin plugin;
    private final PerformanceMonitor performanceMonitor;

    // Thread-safe maps cho server đông người
    private final Map<UUID, Long> lastBreakTime = new ConcurrentHashMap<>();
    private final Map<UUID, Location> lastBreakLocation = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> consecutiveBreaks = new ConcurrentHashMap<>();

    // Cache ore types để tối ưu performance
    private static final Set<Material> ORE_TYPES = EnumSet.of(
        Material.COAL_ORE, Material.IRON_ORE, Material.GOLD_ORE,
        Material.DIAMOND_ORE, Material.EMERALD_ORE, Material.LAPIS_ORE,
        Material.REDSTONE_ORE, Material.COPPER_ORE,
        Material.DEEPSLATE_COAL_ORE, Material.DEEPSLATE_IRON_ORE,
        Material.DEEPSLATE_GOLD_ORE, Material.DEEPSLATE_DIAMOND_ORE,
        Material.DEEPSLATE_EMERALD_ORE, Material.DEEPSLATE_LAPIS_ORE,
        Material.DEEPSLATE_REDSTONE_ORE, Material.DEEPSLATE_COPPER_ORE,
        Material.NETHER_GOLD_ORE, Material.NETHER_QUARTZ_ORE,
        Material.ANCIENT_DEBRIS
    );
    
    // Các giá trị sẽ được load từ config
    private long breakCooldown;
    private int maxConsecutiveBreaks;
    private long spamDelay;
    private double sameAreaRadius;
    private long stuckCheckDelay;
    private boolean debugMode;
    private boolean notifyPlayer;

    // Performance settings
    private long cleanupInterval;
    private long cleanupThreshold;
    private boolean asyncProcessing;
    
    public BlockBreakListener(Plugin plugin) {
        this.plugin = plugin;
        this.performanceMonitor = new PerformanceMonitor(plugin);
        loadConfig();
        startCleanupTask();
    }

    private void loadConfig() {
        // Load config values
        this.breakCooldown = plugin.getConfig().getLong("block-breaking.break-cooldown", 50);
        this.maxConsecutiveBreaks = plugin.getConfig().getInt("block-breaking.max-consecutive-breaks", 20);
        this.spamDelay = plugin.getConfig().getLong("block-breaking.spam-delay", 200);
        this.sameAreaRadius = plugin.getConfig().getDouble("block-breaking.same-area-radius", 10.0);
        this.stuckCheckDelay = plugin.getConfig().getLong("block-breaking.stuck-check-delay", 2);
        this.debugMode = plugin.getConfig().getBoolean("general.debug", false);
        this.notifyPlayer = plugin.getConfig().getBoolean("general.notify-player", true);

        // Load performance settings
        this.cleanupInterval = plugin.getConfig().getLong("performance.cleanup-interval", 300) * 20L; // Convert to ticks
        this.cleanupThreshold = plugin.getConfig().getLong("performance.cleanup-threshold", 300) * 1000L; // Convert to ms
        this.asyncProcessing = plugin.getConfig().getBoolean("performance.async-processing", true);
    }
    
    @EventHandler(priority = EventPriority.HIGHEST)
    public void onBlockBreak(BlockBreakEvent event) {
        performanceMonitor.incrementEventsHandled();

        Player player = event.getPlayer();
        Block block = event.getBlock();
        UUID playerId = player.getUniqueId();
        Location blockLocation = block.getLocation();
        long currentTime = System.currentTimeMillis();

        // Kiểm tra nếu đây là ore được generate bởi AdvancedOreGen
        if (isOreBlock(block.getType())) {
            performanceMonitor.incrementBlocksProcessed();

            // KHÔNG cancel event - để player đào với hiệu suất cao
            // Thay vào đó, đảm bảo block được break thành công

            // Cập nhật tracking data
            lastBreakTime.put(playerId, currentTime);
            lastBreakLocation.put(playerId, blockLocation.clone());

            // Reset consecutive counter nếu đào ở khu vực khác
            Location lastLocation = lastBreakLocation.get(playerId);
            if (lastLocation == null || !isSameArea(blockLocation, lastLocation)) {
                consecutiveBreaks.put(playerId, 0);
            } else {
                consecutiveBreaks.put(playerId, consecutiveBreaks.getOrDefault(playerId, 0) + 1);
            }

            // Đảm bảo block được break - fix client-server desync
            ensureBlockBreakSuccess(player, block);
        }
    }

    // Listener mới để handle stuck blocks
    @EventHandler(priority = EventPriority.MONITOR)
    public void onBlockBreakMonitor(BlockBreakEvent event) {
        if (event.isCancelled()) return;

        Player player = event.getPlayer();
        Block block = event.getBlock();

        if (isOreBlock(block.getType())) {
            // Schedule check để đảm bảo block thực sự bị phá
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Nếu block vẫn còn sau khi event processed
                    if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                        if (debugMode) {
                            plugin.getLogger().warning("Detected stuck block at " +
                                block.getLocation() + " for player " + player.getName());
                        }

                        // Force break và sync với client
                        forceBreakAndSync(player, block);
                        performanceMonitor.incrementForceBreaks();
                    }
                }
            }.runTaskLater(plugin, 1L); // Check sau 1 tick
        }
    }
    
    private boolean isOreBlock(Material material) {
        // O(1) lookup thay vì 19 comparisons
        return ORE_TYPES.contains(material);
    }
    
    private boolean isSameArea(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            return false;
        }

        // Tối ưu: sử dụng distanceSquared để tránh sqrt calculation
        double radiusSquared = sameAreaRadius * sameAreaRadius;
        return loc1.distanceSquared(loc2) <= radiusSquared;
    }
    
    private void forceBreakBlock(Player player, Block block) {
        // Tối ưu: kiểm tra nhanh trước khi xử lý
        if (block.getType() == Material.AIR || !isOreBlock(block.getType())) {
            return;
        }

        if (asyncProcessing) {
            // Async processing để không block main thread
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Double-check trên main thread
                    if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                        // Tạo fake BlockBreakEvent để trigger các plugin khác
                        BlockBreakEvent fakeEvent = new BlockBreakEvent(block, player);
                        Bukkit.getPluginManager().callEvent(fakeEvent);

                        if (!fakeEvent.isCancelled()) {
                            // Break block và drop items
                            block.breakNaturally(player.getInventory().getItemInMainHand());
                        }
                    }
                }
            }.runTask(plugin); // Run on main thread nhưng async
        } else {
            // Synchronous processing
            BlockBreakEvent fakeEvent = new BlockBreakEvent(block, player);
            Bukkit.getPluginManager().callEvent(fakeEvent);

            if (!fakeEvent.isCancelled()) {
                block.breakNaturally(player.getInventory().getItemInMainHand());
            }
        }
    }
    
    // Method mới để đảm bảo block break thành công ngay lập tức
    private void ensureBlockBreakSuccess(Player player, Block block) {
        // Không delay - xử lý ngay để tăng hiệu suất
        if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
            // Sync block state với client ngay lập tức
            player.sendBlockChange(block.getLocation(), Material.AIR.createBlockData());

            if (debugMode) {
                plugin.getLogger().info("Synced block break with client for " + player.getName());
            }
        }
    }

    // Method để force break và sync với client khi phát hiện stuck
    private void forceBreakAndSync(Player player, Block block) {
        if (block.getType() == Material.AIR || !isOreBlock(block.getType())) {
            return;
        }

        // Lưu block type trước khi break
        Material originalType = block.getType();

        // Force break block
        block.breakNaturally(player.getInventory().getItemInMainHand());

        // Đảm bảo client nhận được update
        player.sendBlockChange(block.getLocation(), Material.AIR.createBlockData());

        // Thông báo player nếu được bật
        if (notifyPlayer) {
            player.sendMessage("§aĐã sửa block kẹt!");
        }

        if (debugMode) {
            plugin.getLogger().info("Force broke stuck " + originalType + " at " +
                block.getLocation() + " for player " + player.getName());
        }
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cleanupPlayerData(event.getPlayer().getUniqueId());
    }

    // Reload config
    public void reloadConfig() {
        loadConfig();
    }

    // Periodic cleanup task để tránh memory leaks
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();

                // Cleanup old entries using configured threshold
                lastBreakTime.entrySet().removeIf(entry ->
                    currentTime - entry.getValue() > cleanupThreshold);

                // Cleanup corresponding entries
                lastBreakTime.keySet().retainAll(lastBreakTime.keySet());
                lastBreakLocation.keySet().retainAll(lastBreakTime.keySet());
                consecutiveBreaks.keySet().retainAll(lastBreakTime.keySet());

                if (debugMode) {
                    plugin.getLogger().info("Cleaned up old player data. Active players: " + lastBreakTime.size());
                }
            }
        }.runTaskTimerAsynchronously(plugin, cleanupInterval, cleanupInterval);
    }

    // Listener để detect khi player đang đào nhưng block không break
    @EventHandler(priority = EventPriority.MONITOR)
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.LEFT_CLICK_BLOCK) return;
        if (event.getClickedBlock() == null) return;

        Block block = event.getClickedBlock();
        Player player = event.getPlayer();

        if (isOreBlock(block.getType())) {
            // Schedule check để phát hiện stuck mining animation
            new BukkitRunnable() {
                private int checkCount = 0;

                @Override
                public void run() {
                    checkCount++;

                    // Nếu sau một thời gian block vẫn còn và player vẫn đang nhìn vào block đó
                    if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                        Block targetBlock = player.getTargetBlockExact(6);

                        if (targetBlock != null && targetBlock.equals(block)) {
                            if (debugMode) {
                                plugin.getLogger().warning("Detected stuck mining animation for " +
                                    player.getName() + " at " + block.getLocation());
                            }

                            // Force break stuck block
                            forceBreakAndSync(player, block);
                            performanceMonitor.incrementForceBreaks();
                            this.cancel(); // Stop checking
                        }
                    }

                    // Stop checking after 3 attempts (3 seconds)
                    if (checkCount >= 3 || block.getType() == Material.AIR) {
                        this.cancel();
                    }
                }
            }.runTaskTimer(plugin, 20L, 20L); // Check every second
        }
    }

    // Cleanup data khi player logout
    public void cleanupPlayerData(UUID playerId) {
        lastBreakTime.remove(playerId);
        lastBreakLocation.remove(playerId);
        consecutiveBreaks.remove(playerId);
    }
}
