package soulmc.skymagic.Event;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class BlockBreakListener implements Listener {
    
    private final Plugin plugin;
    
    // Theo dõi thời gian đào block cuối cùng của mỗi player
    private final Map<UUID, Long> lastBreakTime = new HashMap<>();
    
    // Theo dõi vị trí block cuối cùng được đào
    private final Map<UUID, Location> lastBreakLocation = new HashMap<>();
    
    // <PERSON>ố lần đào liên tục
    private final Map<UUID, Integer> consecutiveBreaks = new HashMap<>();
    
    // Các giá trị sẽ được load từ config
    private long breakCooldown;
    private int maxConsecutiveBreaks;
    private long spamDelay;
    private double sameAreaRadius;
    private long stuckCheckDelay;
    private boolean debugMode;
    private boolean notifyPlayer;
    
    public BlockBreakListener(Plugin plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    private void loadConfig() {
        // Load config values
        this.breakCooldown = plugin.getConfig().getLong("block-breaking.break-cooldown", 50);
        this.maxConsecutiveBreaks = plugin.getConfig().getInt("block-breaking.max-consecutive-breaks", 20);
        this.spamDelay = plugin.getConfig().getLong("block-breaking.spam-delay", 200);
        this.sameAreaRadius = plugin.getConfig().getDouble("block-breaking.same-area-radius", 10.0);
        this.stuckCheckDelay = plugin.getConfig().getLong("block-breaking.stuck-check-delay", 2);
        this.debugMode = plugin.getConfig().getBoolean("general.debug", false);
        this.notifyPlayer = plugin.getConfig().getBoolean("general.notify-player", true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        UUID playerId = player.getUniqueId();
        Location blockLocation = block.getLocation();
        long currentTime = System.currentTimeMillis();
        
        // Kiểm tra nếu đây là ore được generate bởi AdvancedOreGen
        if (isOreBlock(block.getType())) {
            
            // Lấy thời gian đào cuối cùng
            Long lastTime = lastBreakTime.get(playerId);
            Location lastLocation = lastBreakLocation.get(playerId);
            
            if (lastTime != null) {
                long timeDiff = currentTime - lastTime;
                
                // Nếu đào quá nhanh
                if (timeDiff < breakCooldown) {
                    event.setCancelled(true);

                    if (debugMode) {
                        plugin.getLogger().info("Player " + player.getName() + " đào quá nhanh, delay: " + timeDiff + "ms");
                    }

                    if (notifyPlayer) {
                        player.sendMessage("§eĐào quá nhanh! Vui lòng chậm lại một chút.");
                    }

                    // Delay và thử lại
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            // Kiểm tra lại block có còn tồn tại không
                            if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                                // Force break block
                                forceBreakBlock(player, block);
                            }
                        }
                    }.runTaskLater(plugin, 1L); // Delay 1 tick

                    return;
                }
                
                // Kiểm tra đào liên tục ở cùng khu vực
                if (lastLocation != null && isSameArea(blockLocation, lastLocation)) {
                    int consecutive = consecutiveBreaks.getOrDefault(playerId, 0) + 1;
                    consecutiveBreaks.put(playerId, consecutive);
                    
                    // Nếu đào quá nhiều liên tục, áp dụng delay
                    if (consecutive > maxConsecutiveBreaks) {
                        event.setCancelled(true);
                        
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                                    forceBreakBlock(player, block);
                                }
                                // Reset counter
                                consecutiveBreaks.put(playerId, 0);
                            }
                        }.runTaskLater(plugin, spamDelay / 50L); // Convert ms to ticks
                        
                        return;
                    }
                } else {
                    // Reset counter nếu đào ở khu vực khác
                    consecutiveBreaks.put(playerId, 0);
                }
            }
            
            // Cập nhật thời gian và vị trí cuối cùng
            lastBreakTime.put(playerId, currentTime);
            lastBreakLocation.put(playerId, blockLocation.clone());
            
            // Đảm bảo block được break thành công
            ensureBlockBreak(player, block);
        }
    }
    
    private boolean isOreBlock(Material material) {
        return material == Material.COAL_ORE || 
               material == Material.IRON_ORE || 
               material == Material.GOLD_ORE || 
               material == Material.DIAMOND_ORE || 
               material == Material.EMERALD_ORE || 
               material == Material.LAPIS_ORE || 
               material == Material.REDSTONE_ORE || 
               material == Material.COPPER_ORE ||
               material == Material.DEEPSLATE_COAL_ORE ||
               material == Material.DEEPSLATE_IRON_ORE ||
               material == Material.DEEPSLATE_GOLD_ORE ||
               material == Material.DEEPSLATE_DIAMOND_ORE ||
               material == Material.DEEPSLATE_EMERALD_ORE ||
               material == Material.DEEPSLATE_LAPIS_ORE ||
               material == Material.DEEPSLATE_REDSTONE_ORE ||
               material == Material.DEEPSLATE_COPPER_ORE ||
               material == Material.NETHER_GOLD_ORE ||
               material == Material.NETHER_QUARTZ_ORE ||
               material == Material.ANCIENT_DEBRIS;
    }
    
    private boolean isSameArea(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            return false;
        }
        
        // Coi là cùng khu vực nếu trong bán kính được cấu hình
        return loc1.distance(loc2) <= sameAreaRadius;
    }
    
    private void forceBreakBlock(Player player, Block block) {
        // Simulate block break với proper drops và experience
        if (block.getType() != Material.AIR) {
            // Tạo fake BlockBreakEvent để trigger các plugin khác
            BlockBreakEvent fakeEvent = new BlockBreakEvent(block, player);
            Bukkit.getPluginManager().callEvent(fakeEvent);
            
            if (!fakeEvent.isCancelled()) {
                // Break block và drop items
                block.breakNaturally(player.getInventory().getItemInMainHand());
            }
        }
    }
    
    private void ensureBlockBreak(Player player, Block block) {
        // Đảm bảo block được break sau 1 tick nếu vẫn còn tồn tại
        new BukkitRunnable() {
            @Override
            public void run() {
                if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                    // Block vẫn còn, có thể bị kẹt
                    if (debugMode) {
                        plugin.getLogger().warning("Phát hiện block kẹt tại " +
                            block.getLocation().toString() + " cho player " + player.getName());
                    }

                    // Thông báo player nếu được bật
                    if (notifyPlayer) {
                        player.sendMessage("§cPhát hiện block kẹt! Đang tự động sửa...");
                    }

                    // Force break
                    forceBreakBlock(player, block);
                }
            }
        }.runTaskLater(plugin, stuckCheckDelay); // Check sau delay được cấu hình
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cleanupPlayerData(event.getPlayer().getUniqueId());
    }

    // Reload config
    public void reloadConfig() {
        loadConfig();
    }

    // Cleanup data khi player logout
    public void cleanupPlayerData(UUID playerId) {
        lastBreakTime.remove(playerId);
        lastBreakLocation.remove(playerId);
        consecutiveBreaks.remove(playerId);
    }
}
