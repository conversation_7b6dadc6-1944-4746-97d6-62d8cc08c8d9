package soulmc.skymagic.Event;

import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;
import soulmc.skymagic.utils.PerformanceMonitor;

import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.EnumSet;

public class BlockBreakListener implements Listener {
    
    private final Plugin plugin;
    private final PerformanceMonitor performanceMonitor;

    // Thread-safe maps cho server đông người
    private final Map<UUID, Long> lastBreakTime = new ConcurrentHashMap<>();
    private final Map<UUID, Location> lastBreakLocation = new ConcurrentHashMap<>();
    private final Map<UUID, Integer> consecutiveBreaks = new ConcurrentHashMap<>();

    // Cache ore types để tối ưu performance
    private static final Set<Material> ORE_TYPES = EnumSet.of(
        Material.COAL_ORE, Material.IRON_ORE, Material.GOLD_ORE,
        Material.DIAMOND_ORE, Material.EMERALD_ORE, Material.LAPIS_ORE,
        Material.REDSTONE_ORE, Material.COPPER_ORE,
        Material.DEEPSLATE_COAL_ORE, Material.DEEPSLATE_IRON_ORE,
        Material.DEEPSLATE_GOLD_ORE, Material.DEEPSLATE_DIAMOND_ORE,
        Material.DEEPSLATE_EMERALD_ORE, Material.DEEPSLATE_LAPIS_ORE,
        Material.DEEPSLATE_REDSTONE_ORE, Material.DEEPSLATE_COPPER_ORE,
        Material.NETHER_GOLD_ORE, Material.NETHER_QUARTZ_ORE,
        Material.ANCIENT_DEBRIS
    );
    
    // Các giá trị sẽ được load từ config
    private long breakCooldown;
    private int maxConsecutiveBreaks;
    private long spamDelay;
    private double sameAreaRadius;
    private long stuckCheckDelay;
    private boolean debugMode;
    private boolean notifyPlayer;

    // Performance settings
    private long cleanupInterval;
    private long cleanupThreshold;
    private boolean asyncProcessing;
    
    public BlockBreakListener(Plugin plugin) {
        this.plugin = plugin;
        this.performanceMonitor = new PerformanceMonitor(plugin);
        loadConfig();
        startCleanupTask();
    }

    private void loadConfig() {
        // Load config values
        this.breakCooldown = plugin.getConfig().getLong("block-breaking.break-cooldown", 50);
        this.maxConsecutiveBreaks = plugin.getConfig().getInt("block-breaking.max-consecutive-breaks", 20);
        this.spamDelay = plugin.getConfig().getLong("block-breaking.spam-delay", 200);
        this.sameAreaRadius = plugin.getConfig().getDouble("block-breaking.same-area-radius", 10.0);
        this.stuckCheckDelay = plugin.getConfig().getLong("block-breaking.stuck-check-delay", 2);
        this.debugMode = plugin.getConfig().getBoolean("general.debug", false);
        this.notifyPlayer = plugin.getConfig().getBoolean("general.notify-player", true);

        // Load performance settings
        this.cleanupInterval = plugin.getConfig().getLong("performance.cleanup-interval", 300) * 20L; // Convert to ticks
        this.cleanupThreshold = plugin.getConfig().getLong("performance.cleanup-threshold", 300) * 1000L; // Convert to ms
        this.asyncProcessing = plugin.getConfig().getBoolean("performance.async-processing", true);
    }
    
    @EventHandler(priority = EventPriority.HIGH)
    public void onBlockBreak(BlockBreakEvent event) {
        performanceMonitor.incrementEventsHandled();

        Player player = event.getPlayer();
        Block block = event.getBlock();
        UUID playerId = player.getUniqueId();
        Location blockLocation = block.getLocation();
        long currentTime = System.currentTimeMillis();

        // Kiểm tra nếu đây là ore được generate bởi AdvancedOreGen
        if (isOreBlock(block.getType())) {
            performanceMonitor.incrementBlocksProcessed();
            
            // Lấy thời gian đào cuối cùng
            Long lastTime = lastBreakTime.get(playerId);
            Location lastLocation = lastBreakLocation.get(playerId);
            
            if (lastTime != null) {
                long timeDiff = currentTime - lastTime;
                
                // Nếu đào quá nhanh
                if (timeDiff < breakCooldown) {
                    event.setCancelled(true);
                    performanceMonitor.incrementCooldownHits();

                    if (debugMode) {
                        plugin.getLogger().info("Player " + player.getName() + " đào quá nhanh, delay: " + timeDiff + "ms");
                    }

                    if (notifyPlayer) {
                        player.sendMessage("§eĐào quá nhanh! Vui lòng chậm lại một chút.");
                    }

                    // Delay và thử lại
                    new BukkitRunnable() {
                        @Override
                        public void run() {
                            // check block có còn tồn tại không
                            if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                                // Force break block
                                performanceMonitor.incrementForceBreaks();
                                forceBreakBlock(player, block);
                            }
                        }
                    }.runTaskLater(plugin, 1L); // Delay 1 tick

                    return;
                }
                
                // Kiểm tra đào liên tục ở cùng khu vực
                if (lastLocation != null && isSameArea(blockLocation, lastLocation)) {
                    int consecutive = consecutiveBreaks.getOrDefault(playerId, 0) + 1;
                    consecutiveBreaks.put(playerId, consecutive);
                    
                    // Nếu đào quá nhiều liên tục, áp dụng delay
                    if (consecutive > maxConsecutiveBreaks) {
                        event.setCancelled(true);
                        
                        new BukkitRunnable() {
                            @Override
                            public void run() {
                                if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                                    forceBreakBlock(player, block);
                                }
                                // Reset counter
                                consecutiveBreaks.put(playerId, 0);
                            }
                        }.runTaskLater(plugin, spamDelay / 50L); // Convert ms to ticks
                        
                        return;
                    }
                } else {
                    // Reset counter nếu đào ở khu vực khác
                    consecutiveBreaks.put(playerId, 0);
                }
            }
            
            // Cập nhật thời gian và vị trí cuối cùng
            lastBreakTime.put(playerId, currentTime);
            // Tối ưu: chỉ clone location khi cần thiết
            if (!lastBreakLocation.containsKey(playerId) ||
                !isSameArea(blockLocation, lastBreakLocation.get(playerId))) {
                lastBreakLocation.put(playerId, blockLocation.clone());
            }
            
            // Đảm bảo block được break thành công
            ensureBlockBreak(player, block);
        }
    }
    
    private boolean isOreBlock(Material material) {
        // O(1) lookup thay vì 19 comparisons
        return ORE_TYPES.contains(material);
    }
    
    private boolean isSameArea(Location loc1, Location loc2) {
        if (!loc1.getWorld().equals(loc2.getWorld())) {
            return false;
        }

        // Tối ưu: sử dụng distanceSquared để tránh sqrt calculation
        double radiusSquared = sameAreaRadius * sameAreaRadius;
        return loc1.distanceSquared(loc2) <= radiusSquared;
    }
    
    private void forceBreakBlock(Player player, Block block) {
        // Tối ưu: kiểm tra nhanh trước khi xử lý
        if (block.getType() == Material.AIR || !isOreBlock(block.getType())) {
            return;
        }

        if (asyncProcessing) {
            // Async processing để không block main thread
            new BukkitRunnable() {
                @Override
                public void run() {
                    // Double-check trên main thread
                    if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                        // Tạo fake BlockBreakEvent để trigger các plugin khác
                        BlockBreakEvent fakeEvent = new BlockBreakEvent(block, player);
                        Bukkit.getPluginManager().callEvent(fakeEvent);

                        if (!fakeEvent.isCancelled()) {
                            // Break block và drop items
                            block.breakNaturally(player.getInventory().getItemInMainHand());
                        }
                    }
                }
            }.runTask(plugin); // Run on main thread nhưng async
        } else {
            // Synchronous processing
            BlockBreakEvent fakeEvent = new BlockBreakEvent(block, player);
            Bukkit.getPluginManager().callEvent(fakeEvent);

            if (!fakeEvent.isCancelled()) {
                block.breakNaturally(player.getInventory().getItemInMainHand());
            }
        }
    }
    
    private void ensureBlockBreak(Player player, Block block) {
        // Đảm bảo block được break sau 1 tick nếu vẫn còn tồn tại
        new BukkitRunnable() {
            @Override
            public void run() {
                if (block.getType() != Material.AIR && isOreBlock(block.getType())) {
                    // Block vẫn còn, có thể bị kẹt
                    if (debugMode) {
                        plugin.getLogger().warning("Phát hiện block kẹt tại " +
                            block.getLocation().toString() + " cho player " + player.getName());
                    }

                    // Thông báo player nếu được bật
                    if (notifyPlayer) {
                        player.sendMessage("§cPhát hiện block kẹt! Đang tự động sửa...");
                    }

                    // Force break
                    forceBreakBlock(player, block);
                }
            }
        }.runTaskLater(plugin, stuckCheckDelay); // Check sau delay được cấu hình
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        cleanupPlayerData(event.getPlayer().getUniqueId());
    }

    // Reload config
    public void reloadConfig() {
        loadConfig();
    }

    // Periodic cleanup task để tránh memory leaks
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();

                // Cleanup old entries using configured threshold
                lastBreakTime.entrySet().removeIf(entry ->
                    currentTime - entry.getValue() > cleanupThreshold);

                // Cleanup corresponding entries
                lastBreakTime.keySet().retainAll(lastBreakTime.keySet());
                lastBreakLocation.keySet().retainAll(lastBreakTime.keySet());
                consecutiveBreaks.keySet().retainAll(lastBreakTime.keySet());

                if (debugMode) {
                    plugin.getLogger().info("Cleaned up old player data. Active players: " + lastBreakTime.size());
                }
            }
        }.runTaskTimerAsynchronously(plugin, cleanupInterval, cleanupInterval);
    }

    // Cleanup data khi player logout
    public void cleanupPlayerData(UUID playerId) {
        lastBreakTime.remove(playerId);
        lastBreakLocation.remove(playerId);
        consecutiveBreaks.remove(playerId);
    }
}
