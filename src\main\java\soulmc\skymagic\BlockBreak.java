package soulmc.skymagic;

import org.bukkit.plugin.java.JavaPlugin;
import soulmc.skymagic.Event.BlockBreakListener;
import soulmc.skymagic.commands.FixBlockBreakCommand;

public class BlockBreak extends JavaPlugin {

    private BlockBreakListener blockBreakListener;

    @Override
    public void onEnable() {
        // Tạo config mặc định nếu chưa có
        saveDefaultConfig();

        // Kiểm tra plugin có được bật không
        if (!getConfig().getBoolean("general.enabled", true)) {
            getLogger().info("Plugin bị tắt trong config!");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Đăng ký event listener
        blockBreakListener = new BlockBreakListener(this);
        getServer().getPluginManager().registerEvents(blockBreakListener, this);

        // Đ<PERSON>ng ký command
        getCommand("fixblockbreak").setExecutor(new FixBlockBreakCommand(this));

        getLogger().info("FixBlockBreak plugin đã được kích hoạt!");
        getLogger().info("Plugin này sẽ giúp fix vấn đề kẹt block khi đào ore liên tục.");

        // Kiểm tra AdvancedOreGen
        if (getServer().getPluginManager().getPlugin("AdvancedOreGen") != null) {
            getLogger().info("Đã phát hiện AdvancedOreGen plugin!");
        } else {
            getLogger().warning("Không tìm thấy AdvancedOreGen plugin!");
        }

        // Hiển thị cài đặt nếu debug mode
        if (getConfig().getBoolean("general.debug", false)) {
            getLogger().info("Debug mode: BẬT");
            getLogger().info("Break cooldown: " + getConfig().getLong("block-breaking.break-cooldown") + "ms");
            getLogger().info("Max consecutive breaks: " + getConfig().getInt("block-breaking.max-consecutive-breaks"));
        }
    }

    @Override
    public void onDisable() {
        getLogger().info("FixBlockBreak plugin đã được tắt!");
    }

    // Method để reload config từ command
    public void reloadPluginConfig() {
        reloadConfig();
        if (blockBreakListener != null) {
            blockBreakListener.reloadConfig();
        }
    }
}