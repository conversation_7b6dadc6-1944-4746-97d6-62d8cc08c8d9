package soulmc.skymagic.utils;

import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.concurrent.atomic.AtomicLong;

public class PerformanceMonitor {
    
    private final Plugin plugin;
    private final AtomicLong totalBlocksProcessed = new AtomicLong(0);
    private final AtomicLong totalEventsHandled = new AtomicLong(0);
    private final AtomicLong totalForceBreaks = new AtomicLong(0);
    private final AtomicLong totalCooldownHits = new AtomicLong(0);
    
    private long startTime;
    
    public PerformanceMonitor(Plugin plugin) {
        this.plugin = plugin;
        this.startTime = System.currentTimeMillis();
        startMonitoring();
    }
    
    private void startMonitoring() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (plugin.getConfig().getBoolean("general.debug", false)) {
                    logPerformanceStats();
                }
            }
        }.runTaskTimerAsynchronously(plugin, 12000L, 12000L); // Every 10 minutes
    }
    
    public void incrementBlocksProcessed() {
        totalBlocksProcessed.incrementAndGet();
    }
    
    public void incrementEventsHandled() {
        totalEventsHandled.incrementAndGet();
    }
    
    public void incrementForceBreaks() {
        totalForceBreaks.incrementAndGet();
    }
    
    public void incrementCooldownHits() {
        totalCooldownHits.incrementAndGet();
    }
    
    private void logPerformanceStats() {
        long uptime = System.currentTimeMillis() - startTime;
        long uptimeMinutes = uptime / 60000;
        
        plugin.getLogger().info("=== FixBlockBreak Performance Stats ===");
        plugin.getLogger().info("Uptime: " + uptimeMinutes + " minutes");
        plugin.getLogger().info("Total events handled: " + totalEventsHandled.get());
        plugin.getLogger().info("Total blocks processed: " + totalBlocksProcessed.get());
        plugin.getLogger().info("Total force breaks: " + totalForceBreaks.get());
        plugin.getLogger().info("Total cooldown hits: " + totalCooldownHits.get());
        
        if (uptimeMinutes > 0) {
            plugin.getLogger().info("Events per minute: " + (totalEventsHandled.get() / uptimeMinutes));
            plugin.getLogger().info("Blocks per minute: " + (totalBlocksProcessed.get() / uptimeMinutes));
        }
        
        // Memory usage
        Runtime runtime = Runtime.getRuntime();
        long usedMemory = runtime.totalMemory() - runtime.freeMemory();
        plugin.getLogger().info("Memory usage: " + (usedMemory / 1024 / 1024) + " MB");
    }
    
    public void reset() {
        totalBlocksProcessed.set(0);
        totalEventsHandled.set(0);
        totalForceBreaks.set(0);
        totalCooldownHits.set(0);
        startTime = System.currentTimeMillis();
    }
    
    // Getters for external access
    public long getTotalBlocksProcessed() { return totalBlocksProcessed.get(); }
    public long getTotalEventsHandled() { return totalEventsHandled.get(); }
    public long getTotalForceBreaks() { return totalForceBreaks.get(); }
    public long getTotalCooldownHits() { return totalCooldownHits.get(); }
}
