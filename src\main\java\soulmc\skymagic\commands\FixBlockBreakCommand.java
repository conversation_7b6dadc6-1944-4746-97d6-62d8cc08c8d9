package soulmc.skymagic.commands;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.format.NamedTextColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import soulmc.skymagic.BlockBreak;

public class FixB<PERSON><PERSON>reakCommand implements CommandExecutor {
    
    private final BlockBreak plugin;
    
    public FixBlockBreakCommand(BlockBreak plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        if (!sender.hasPermission("fixblockbreak.admin")) {
            sender.sendMessage(Component.text("Bạn không có quyền sử dụng lệnh này!", NamedTextColor.RED));
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "info":
                sendInfoMessage(sender);
                break;
                
            case "reload":
                reloadPlugin(sender);
                break;
                
            case "status":
                sendStatusMessage(sender);
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(Component.text("=== FixBlockBreak Commands ===", NamedTextColor.GOLD));
        sender.sendMessage(Component.text("/fixblockbreak info", NamedTextColor.YELLOW)
            .append(Component.text(" - Thông tin plugin", NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("/fixblockbreak status", NamedTextColor.YELLOW)
            .append(Component.text(" - Trạng thái plugin", NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("/fixblockbreak reload", NamedTextColor.YELLOW)
            .append(Component.text(" - Reload plugin", NamedTextColor.WHITE)));
    }
    
    private void sendInfoMessage(CommandSender sender) {
        sender.sendMessage(Component.text("=== FixBlockBreak Info ===", NamedTextColor.GOLD));
        sender.sendMessage(Component.text("Plugin: ", NamedTextColor.GREEN)
            .append(Component.text(plugin.getPluginMeta().getName(), NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Version: ", NamedTextColor.GREEN)
            .append(Component.text(plugin.getPluginMeta().getVersion(), NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Author: ", NamedTextColor.GREEN)
            .append(Component.text(plugin.getPluginMeta().getAuthors().toString(), NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Mô tả: ", NamedTextColor.GREEN)
            .append(Component.text("Fix vấn đề kẹt block khi đào ore liên tục", NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Plugin này giúp giải quyết vấn đề kẹt block khi sử dụng AdvancedOreGen", NamedTextColor.YELLOW));
    }
    
    private void sendStatusMessage(CommandSender sender) {
        sender.sendMessage(Component.text("=== FixBlockBreak Status ===", NamedTextColor.GOLD));
        sender.sendMessage(Component.text("Plugin đang hoạt động: ", NamedTextColor.GREEN)
            .append(Component.text("✓", NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Server version: ", NamedTextColor.GREEN)
            .append(Component.text(plugin.getServer().getVersion(), NamedTextColor.WHITE)));
        sender.sendMessage(Component.text("Bukkit version: ", NamedTextColor.GREEN)
            .append(Component.text(plugin.getServer().getBukkitVersion(), NamedTextColor.WHITE)));

        // Kiểm tra AdvancedOreGen
        if (plugin.getServer().getPluginManager().getPlugin("AdvancedOreGen") != null) {
            sender.sendMessage(Component.text("AdvancedOreGen: ", NamedTextColor.GREEN)
                .append(Component.text("✓ Đã phát hiện", NamedTextColor.WHITE)));
        } else {
            sender.sendMessage(Component.text("AdvancedOreGen: ", NamedTextColor.YELLOW)
                .append(Component.text("✗ Không tìm thấy", NamedTextColor.WHITE)));
        }
    }
    
    private void reloadPlugin(CommandSender sender) {
        sender.sendMessage(Component.text("Đang reload FixBlockBreak plugin...", NamedTextColor.YELLOW));

        try {
            // Reload config và listener
            plugin.reloadPluginConfig();

            sender.sendMessage(Component.text("✓ Plugin đã được reload thành công!", NamedTextColor.GREEN));
        } catch (Exception e) {
            sender.sendMessage(Component.text("✗ Lỗi khi reload plugin: " + e.getMessage(), NamedTextColor.RED));
            plugin.getLogger().severe("Lỗi khi reload plugin: " + e.getMessage());
        }
    }
}
