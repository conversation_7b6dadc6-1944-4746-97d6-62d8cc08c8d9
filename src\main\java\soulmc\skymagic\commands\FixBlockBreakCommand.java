package soulmc.skymagic.commands;

import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import soulmc.skymagic.BlockBreak;

public class FixBlockBreakCommand implements CommandExecutor {
    
    private final BlockBreak plugin;
    
    public FixBlockBreakCommand(BlockBreak plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        
        if (!sender.hasPermission("fixblockbreak.admin")) {
            sender.sendMessage(ChatColor.RED + "Bạn không có quyền sử dụng lệnh này!");
            return true;
        }
        
        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "info":
                sendInfoMessage(sender);
                break;
                
            case "reload":
                reloadPlugin(sender);
                break;
                
            case "status":
                sendStatusMessage(sender);
                break;
                
            default:
                sendHelpMessage(sender);
                break;
        }
        
        return true;
    }
    
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== FixBlockBreak Commands ===");
        sender.sendMessage(ChatColor.YELLOW + "/fixblockbreak info" + ChatColor.WHITE + " - Thông tin plugin");
        sender.sendMessage(ChatColor.YELLOW + "/fixblockbreak status" + ChatColor.WHITE + " - Trạng thái plugin");
        sender.sendMessage(ChatColor.YELLOW + "/fixblockbreak reload" + ChatColor.WHITE + " - Reload plugin");
    }
    
    private void sendInfoMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== FixBlockBreak Info ===");
        sender.sendMessage(ChatColor.GREEN + "Plugin: " + ChatColor.WHITE + plugin.getDescription().getName());
        sender.sendMessage(ChatColor.GREEN + "Version: " + ChatColor.WHITE + plugin.getDescription().getVersion());
        sender.sendMessage(ChatColor.GREEN + "Author: " + ChatColor.WHITE + plugin.getDescription().getAuthors());
        sender.sendMessage(ChatColor.GREEN + "Mô tả: " + ChatColor.WHITE + "Fix vấn đề kẹt block khi đào ore liên tục");
        sender.sendMessage(ChatColor.YELLOW + "Plugin này giúp giải quyết vấn đề kẹt block khi sử dụng AdvancedOreGen");
    }
    
    private void sendStatusMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "=== FixBlockBreak Status ===");
        sender.sendMessage(ChatColor.GREEN + "Plugin đang hoạt động: " + ChatColor.WHITE + "✓");
        sender.sendMessage(ChatColor.GREEN + "Server version: " + ChatColor.WHITE + plugin.getServer().getVersion());
        sender.sendMessage(ChatColor.GREEN + "Bukkit version: " + ChatColor.WHITE + plugin.getServer().getBukkitVersion());
        
        // Kiểm tra AdvancedOreGen
        if (plugin.getServer().getPluginManager().getPlugin("AdvancedOreGen") != null) {
            sender.sendMessage(ChatColor.GREEN + "AdvancedOreGen: " + ChatColor.WHITE + "✓ Đã phát hiện");
        } else {
            sender.sendMessage(ChatColor.YELLOW + "AdvancedOreGen: " + ChatColor.WHITE + "✗ Không tìm thấy");
        }
    }
    
    private void reloadPlugin(CommandSender sender) {
        sender.sendMessage(ChatColor.YELLOW + "Đang reload FixBlockBreak plugin...");
        
        try {
            // Disable và enable lại plugin
            plugin.getServer().getPluginManager().disablePlugin(plugin);
            plugin.getServer().getPluginManager().enablePlugin(plugin);
            
            sender.sendMessage(ChatColor.GREEN + "✓ Plugin đã được reload thành công!");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "✗ Lỗi khi reload plugin: " + e.getMessage());
            plugin.getLogger().severe("Lỗi khi reload plugin: " + e.getMessage());
        }
    }
}
