# FixBlockBreak Plugin

Plugin Minecraft để fix vấn đề kẹt block khi đào ore liên tục, đặc biệt khi sử dụng với plugin AdvancedOreGen.

## Vấn đề được giải quyết

Khi đào ore liên tục ở máy farm ore (nước + hàng rào) trên server 1.21.4, thường xảy ra tình trạng:
- Block bị kẹt không đào được sau một thời gian
- Phải đợi hoặc relog để tiếp tục đào
- Ảnh hưởng đến hiệu quả farm ore

## Nguyên nhân

1. **Rate limiting**: Server có cơ chế chống spam block breaking
2. **Event conflict**: Xung đột giữa các event BlockBreakEvent
3. **Plugin compatibility**: Vấn đề tương thích với AdvancedOreGen
4. **Memory issues**: Tích lũy dữ liệu không được giải phóng

## Tính năng

- ✅ Tự động phát hiện và fix block kẹt
- ✅ Cooldown thông minh giữa các lần đào
- ✅ Chống spam đào quá nhanh
- ✅ Tương thích với AdvancedOreGen
- ✅ Cấu hình linh hoạt
- ✅ Debug mode để theo dõi
- ✅ Commands quản lý
- 🚀 **Tối ưu cho server đông người**
- 🚀 **Thread-safe operations**
- 🚀 **Async processing**
- 🚀 **Performance monitoring**
- 🚀 **Memory leak prevention**

## Cài đặt

1. Tải file `.jar` vào thư mục `plugins/`
2. Khởi động lại server
3. Chỉnh sửa `config.yml` nếu cần
4. Sử dụng `/fixblockbreak reload` để áp dụng config mới

## Commands

- `/fixblockbreak info` - Thông tin plugin
- `/fixblockbreak status` - Trạng thái plugin
- `/fixblockbreak reload` - Reload plugin

## Permissions

- `fixblockbreak.admin` - Quyền sử dụng commands (mặc định: OP)

## Cấu hình

File `config.yml` cho phép tùy chỉnh:

```yaml
# Cài đặt chung
general:
  enabled: true          # Bật/tắt plugin
  debug: false          # Debug mode
  notify-player: true   # Thông báo player khi fix

# Cài đặt block breaking
block-breaking:
  break-cooldown: 50              # Cooldown giữa các lần đào (ms)
  max-consecutive-breaks: 20      # Số lần đào liên tục tối đa
  spam-delay: 200                 # Delay khi spam (ms)
  same-area-radius: 10.0          # Bán kính "cùng khu vực"
  stuck-check-delay: 2            # Delay kiểm tra kẹt (ticks)

# Tối ưu performance cho server đông người
performance:
  cleanup-interval: 300           # Thời gian cleanup (seconds)
  cleanup-threshold: 300          # Threshold cleanup (seconds)
  async-processing: true          # Bật async processing
  batch-size: 10                  # Batch size xử lý
```

## Cách hoạt động

1. **Phát hiện đào nhanh**: Nếu đào quá nhanh, delay và thử lại
2. **Chống spam**: Nếu đào quá nhiều liên tục, áp dụng delay dài hơn
3. **Kiểm tra kẹt**: Tự động kiểm tra và fix block kẹt
4. **Force break**: Buộc break block nếu bị kẹt

## Tương thích

- ✅ Minecraft 1.21.4
- ✅ Paper/Spigot/Bukkit
- ✅ AdvancedOreGen
- ✅ Java 21

## Hỗ trợ

Nếu gặp vấn đề:
1. Bật debug mode trong config
2. Kiểm tra console logs
3. Sử dụng `/fixblockbreak status`
4. Báo cáo lỗi với thông tin chi tiết

## Build từ source

```bash
mvn clean package
```

File `.jar` sẽ được tạo trong thư mục `target/`
